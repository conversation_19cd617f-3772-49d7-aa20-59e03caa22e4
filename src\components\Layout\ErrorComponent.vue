<template>
   <div class="flex h-full flex-col items-center justify-center">
      <div class="w-80">
         <v-responsive>
            <svg
               height="100%"
               xml:space="preserve"
               style="fill-rule: evenodd; clip-rule: evenodd; stroke-miterlimit: 10"
               version="1.1"
               viewBox="0 0 1750 720"
               width="100%"
               xmlns:xlink="http://www.w3.org/1999/xlink"
               xmlns="http://www.w3.org/2000/svg">
               <g transform="matrix(4.16667,0,0,4.16667,1683.32,473.336)">
                  <rect
                     height="23.846"
                     style="fill: rgb(253, 155, 105); fill-rule: nonzero"
                     width="33.017"
                     x="-33.017"
                     y="-23.846" />
               </g>
               <g transform="matrix(-4.16667,0,0,4.16667,3356.7,-550.903)">
                  <rect
                     height="30.266"
                     style="fill: rgb(253, 226, 212)"
                     width="23.846"
                     x="410.783"
                     y="245.817" />
               </g>
               <g transform="matrix(-4.16667,0,0,4.16667,3356.7,-955.978)">
                  <rect
                     height="19.26"
                     style="fill: rgb(157, 114, 136)"
                     width="23.846"
                     x="410.783"
                     y="299.929" />
               </g>
               <g transform="matrix(4.16667,0,0,4.16667,1645.11,293.729)">
                  <rect
                     height="44.023"
                     style="fill: rgb(255, 115, 32); fill-rule: nonzero"
                     width="23.846"
                     x="-23.846"
                     y="-44.023" />
               </g>
               <g transform="matrix(4.16667,0,0,4.16667,1545.75,473.336)">
                  <path
                     d="M0,-23.846L0,0L-34.229,0L-15.408,-23.827L-15.391,-23.846L0,-23.846Z"
                     style="fill: rgb(198, 178, 203); fill-rule: nonzero" />
               </g>
               <g transform="matrix(4.16667,0,0,4.16667,1545.75,373.976)">
                  <path
                     d="M0,-63.732L0,-19.7L-7.741,-9.85L-15.519,0L-47.123,0L0,-63.732Z"
                     style="fill: rgb(255, 164, 53); fill-rule: nonzero" />
               </g>
               <g transform="matrix(4.16667,0,0,4.16667,1481.09,473.336)">
                  <path
                     d="M-31.266,-23.846L0.083,-23.846L-18.756,0L-48.893,0L-31.266,-23.846Z"
                     style="fill: rgb(253, 226, 212); fill-rule: nonzero" />
               </g>
               <g transform="matrix(4.16667,0,0,4.16667,1441.4,331.12)">
                  <path
                     d="M15.481,9.385L-0.038,9.385L7.74,-0.466L15.481,-10.317L15.481,9.385ZM40.351,-54.344L15.481,-54.344L-15.388,-12.6M-31.646,9.385L-49.424,33.429L15.481,33.429L15.481,63.729L39.404,63.729L39.404,33.429M48.751,33.429L48.751,9.385L39.404,9.385L39.434,-40.466"
                     style="fill: none; fill-rule: nonzero; stroke: rgb(0, 18, 69); stroke-width: 5px" />
               </g>
               <g transform="matrix(4.16667,0,0,4.16667,1163.99,472.09)">
                  <path
                     d="M0,-49.845L0,-0.169L-0.468,0.299L-38.263,0.299C-31.144,-5.82 -26.634,-14.885 -26.634,-25.007C-26.634,-35.035 -31.06,-44.026 -38.066,-50.144L-0.3,-50.144L0,-49.845Z"
                     style="fill: rgb(255, 115, 32); fill-rule: nonzero" />
               </g>
               <g transform="matrix(-4.16667,0,0,4.16667,967.436,118.539)">
                  <path
                     d="M-46.715,34.708L-9.02,34.708C-6.359,32.393 -3.315,30.478 0,29.096L-12.008,0L-46.715,34.708Z"
                     style="fill: rgb(253, 226, 212); fill-rule: nonzero" />
               </g>
               <g transform="matrix(4.16667,0,0,4.16667,1162.1,614.73)">
                  <path
                     d="M0,-34.852L-34.851,0L-35.058,0L-47.136,-29.265C-43.787,-30.638 -40.702,-32.538 -38.002,-34.852L0,-34.852Z"
                     style="fill: rgb(157, 114, 136); fill-rule: nonzero" />
               </g>
               <g transform="matrix(4.16667,0,0,4.16667,1017.47,239.175)">
                  <path
                     d="M0,-29.04L-12.008,0.056C-15.958,-1.597 -20.3,-2.512 -24.848,-2.512C-29.376,-2.512 -33.69,-1.606 -37.621,0.028L-49.639,-29.096L-0.057,-29.096L0,-29.04Z"
                     style="fill: rgb(255, 164, 53); fill-rule: nonzero" />
               </g>
               <g transform="matrix(4.16667,0,0,4.16667,966.424,614.727)">
                  <path
                     d="M0,-29.049L11.989,0L-37.107,0L-25.137,-29.03C-21.262,-27.452 -17.031,-26.584 -12.597,-26.584C-8.143,-26.584 -3.885,-27.462 0,-29.049"
                     style="fill: rgb(253, 155, 105); fill-rule: nonzero" />
               </g>
               <g transform="matrix(4.16667,0,0,4.16667,862.41,591.37)">
                  <path
                     d="M0,-23.639L-12.059,5.606L-12.341,5.606L-47.193,-29.245L-9.19,-29.245C-6.472,-26.912 -3.377,-25.013 0,-23.639"
                     style="fill: rgb(253, 226, 212); fill-rule: nonzero" />
               </g>
               <g transform="matrix(1.58937,3.85162,3.85162,-1.58937,848.28,206.908)">
                  <path
                     d="M-23.102,-0.239L8.404,-0.239C8.415,-3.86 9.016,-7.421 10.152,-10.789L-4.227,-45.634L-23.102,-0.239Z"
                     style="fill: rgb(198, 178, 203); fill-rule: nonzero" />
               </g>
               <g transform="matrix(4.16667,0,0,4.16667,822.806,263.155)">
                  <path
                     d="M0,50.443L-37.795,50.443L-38.263,49.975L-38.263,0.299L-37.963,0L-0.197,0C-7.204,6.118 -11.628,15.109 -11.628,25.137C-11.628,35.26 -7.12,44.325 0,50.443"
                     style="fill: rgb(198, 178, 203); fill-rule: nonzero" />
               </g>
               <g transform="matrix(4.16667,0,0,4.16667,824.531,236.736)">
                  <path
                     d="M0,61.646C-3.352,60.283 -6.424,58.397 -9.123,56.081C-16.229,49.974 -20.729,40.926 -20.729,30.823C-20.729,20.813 -16.312,11.84 -9.319,5.733C-6.639,3.399 -3.576,1.484 -0.233,0.093C3.698,-1.541 8.012,-2.447 12.541,-2.447M34.4,5.733C41.393,11.84 45.81,20.813 45.81,30.823C45.81,40.926 41.309,49.974 34.204,56.081C31.524,58.378 28.461,60.264 25.137,61.627C21.252,63.215 16.995,64.093 12.541,64.093M72.394,6.032L37.332,-29.03L-12.25,-29.03M-24.723,-16.558L-47.313,6.032L-47.313,55.614L-29.612,73.315M-12.25,90.676L37.332,90.676L47.382,80.626M57.837,70.171L72.394,55.614"
                     style="fill: none; fill-rule: nonzero; stroke: rgb(0, 18, 69); stroke-width: 5px" />
               </g>
               <g transform="matrix(4.16667,0,0,4.16667,552.557,473.336)">
                  <rect
                     height="23.846"
                     style="fill: rgb(253, 155, 105); fill-rule: nonzero"
                     width="33.017"
                     x="-33.017"
                     y="-23.846" />
               </g>
               <g transform="matrix(-4.16667,0,0,4.16667,1095.18,-550.903)">
                  <rect
                     height="30.266"
                     style="fill: rgb(255, 115, 32)"
                     width="23.846"
                     x="139.4"
                     y="245.817" />
               </g>
               <g transform="matrix(-4.16667,0,0,4.16667,1095.18,-955.978)">
                  <rect
                     height="19.26"
                     style="fill: rgb(157, 114, 136)"
                     width="23.846"
                     x="139.4"
                     y="299.929" />
               </g>
               <g transform="matrix(-4.16667,0,0,4.16667,1095.18,-1219.65)">
                  <rect
                     height="44.023"
                     style="fill: rgb(198, 178, 203)"
                     width="23.846"
                     x="139.4"
                     y="319.188" />
               </g>
               <g transform="matrix(4.16667,0,0,4.16667,414.985,473.336)">
                  <path
                     d="M0,-23.846L0,0L-34.227,0L-15.391,-23.846L0,-23.846Z"
                     style="fill: rgb(198, 178, 203); fill-rule: nonzero" />
               </g>
               <g transform="matrix(4.16667,0,0,4.16667,414.985,373.98)">
                  <path
                     d="M0,-63.729L0,-19.703L-7.741,-9.852L-15.519,0L-47.126,0L0,-63.729Z"
                     style="fill: rgb(253, 226, 212); fill-rule: nonzero" />
               </g>
               <g transform="matrix(-2.58271,3.26966,3.26966,2.58271,224.356,534.523)">
                  <path
                     d="M-49.026,-0.096L-18.638,-0.096L0.047,-23.75L-29.595,-24.695L-49.026,-0.096Z"
                     style="fill: rgb(255, 164, 53); fill-rule: nonzero" />
               </g>
               <g transform="matrix(4.16667,0,0,4.16667,310.62,331.12)">
                  <path
                     d="M15.481,9.385L-0.038,9.385L7.741,-0.466L15.481,-10.317L15.481,9.385ZM40.351,-54.344L15.481,-54.344L-15.388,-12.6M-31.645,9.385L-49.424,33.429L15.481,33.429L15.481,63.729L39.404,63.729L39.404,33.429M48.751,33.429L48.751,9.385L39.404,9.385L39.404,-40.466"
                     style="fill: none; fill-rule: nonzero; stroke: rgb(0, 18, 69); stroke-width: 5px" />
               </g>
            </svg>
         </v-responsive>
      </div>

      <div>Aradığınız sayfa bulunamadı</div>
   </div>
</template>

import {
   IconAdjustments,
   IconAlertSquareRoundedFilled,
   IconArrowBigUp,
   IconArrowDown,
   IconArrowLeft,
   IconArrowRight,
   IconArrowUp,
   IconBackspace,
   IconBell,
   IconBrowser,
   IconBuildings,
   IconBuildingWarehouse,
   IconCalendar,
   IconCaretDownFilled,
   IconCaretRightFilled,
   IconCheck,
   IconChevronCompactUp,
   IconChevronDown,
   IconChevronLeft,
   IconChevronLeftPipe,
   IconChevronRight,
   IconChevronRightPipe,
   IconChevronUp,
   IconCircle,
   IconCircleCheckFilled,
   IconCircleDotFilled,
   IconCircleFilled,
   IconCircleX,
   IconCloudUpload,
   IconColorPicker,
   IconCommand,
   IconCopy,
   IconCurrencyLira,
   IconDeviceFloppy,
   IconDeviceIpadHorizontal,
   IconDeviceIpadHorizontalQuestion,
   IconDevicesPc,
   IconDotsVertical,
   IconGraph,
   IconHeartHandshake,
   IconHome,
   IconInfoSquareRoundedFilled,
   IconLanguage,
   IconLayoutGridAdd,
   IconLicense,
   IconLink,
   IconListCheck,
   IconListDetails,
   IconLogin,
   IconLogout,
   IconMaximize,
   IconMenu2,
   IconMinimize,
   IconMinus,
   IconMoonStars,
   IconPalette,
   IconPaperclip,
   IconPencil,
   IconPercentage,
   IconPlayerPauseFilled,
   IconPlayerPlayFilled,
   IconPlus,
   IconQuestionMark,
   IconRefresh,
   IconRepeat,
   IconReplace,
   IconRuler2,
   IconSearch,
   IconSelector,
   IconSettings,
   IconShare,
   IconSlash,
   IconSortAscending,
   IconSortDescending,
   IconSpace,
   IconSparkles,
   IconSquareRounded,
   IconSquareRoundedCheckFilled,
   IconSquareRoundedMinusFilled,
   IconStar,
   IconStarFilled,
   IconStarHalfFilled,
   IconSun,
   IconTags,
   IconTarget,
   IconTie,
   IconTools,
   IconTransferVertical,
   IconTrash,
   IconUser,
   IconUsers,
   IconVolume,
   IconVolume2,
   IconVolume3,
   IconVolumeOff,
   IconX
} from "@tabler/icons-vue";

export const tablerAliases = {
   // vuetify start
   collapse: IconChevronUp,
   complete: IconCheck,
   cancel: IconCircleX,
   close: IconX,
   delete: IconCircleX,
   clear: IconX,
   success: IconCircleCheckFilled,
   info: IconInfoSquareRoundedFilled,
   warning: IconAlertSquareRoundedFilled,
   error: IconCircleX,
   prev: IconChevronLeft,
   next: IconChevronRight,
   checkboxOn: IconSquareRoundedCheckFilled,
   checkboxOff: IconSquareRounded,
   checkboxIndeterminate: IconSquareRoundedMinusFilled,
   delimiter: IconCircleFilled,
   sortAsc: IconSortAscending,
   sortDesc: IconSortDescending,
   expand: IconChevronDown,
   menu: IconMenu2,
   subgroup: IconCaretDownFilled,
   dropdown: IconCaretDownFilled,
   radioOn: IconCircleDotFilled,
   radioOff: IconCircle,
   edit: IconPencil,
   ratingEmpty: IconStar,
   ratingFull: IconStarFilled,
   ratingHalf: IconStarHalfFilled,
   loading: IconRefresh,
   first: IconChevronLeftPipe,
   last: IconChevronRightPipe,
   unfold: IconSelector,
   file: IconPaperclip,
   plus: IconPlus,
   minus: IconMinus,
   calendar: IconCalendar,
   treeviewCollapse: IconCaretDownFilled,
   treeviewExpand: IconCaretRightFilled,
   eyeDropper: IconColorPicker,
   upload: IconCloudUpload,
   color: IconPalette,
   command: IconCommand,
   ctrl: IconChevronCompactUp,
   space: IconSpace,
   shift: IconArrowBigUp,
   alt: IconTransferVertical,
   enter: IconLogin,
   arrowup: IconArrowUp,
   arrowdown: IconArrowDown,
   arrowleft: IconArrowLeft,
   arrowright: IconArrowRight,
   backspace: IconBackspace,
   play: IconPlayerPlayFilled,
   pause: IconPlayerPauseFilled,
   fullscreen: IconMaximize,
   fullscreenExit: IconMinimize,
   volumeHigh: IconVolume,
   volumeMedium: IconVolume2,
   volumeLow: IconVolume3,
   volumeOff: IconVolumeOff,
   // vuetify end

   sparkles: IconSparkles,
   save: IconDeviceFloppy,
   question: IconQuestionMark,
   exclamation: IconAlertSquareRoundedFilled,
   percent: IconPercentage,
   notification: IconBell,
   slash: IconSlash,
   search: IconSearch,
   dots: IconDotsVertical,
   translate: IconLanguage,
   settings: IconSettings,
   definitions: IconAdjustments,
   trash: IconTrash,
   copy: IconCopy,
   ruler: IconRuler2,
   link: IconLink,
   reset: IconRefresh,
   user: IconUser,
   userGroup: IconUsers,
   login: IconLogin,
   logout: IconLogout,
   moon: IconMoonStars,
   sun: IconSun,
   tags: IconTags,
   tools: IconTools,
   browser: IconBrowser,
   target: IconTarget,
   handShake: IconHeartHandshake,
   replace: IconReplace,
   graph: IconGraph,
   task: IconListCheck,
   company: IconBuildings,
   detail: IconListDetails,
   gridAdd: IconLayoutGridAdd,
   tie: IconTie,
   equipment: IconDevicesPc,
   license: IconLicense,
   subscription: IconRepeat,
   share: IconShare,
   home: IconHome,
   warehouse: IconBuildingWarehouse,
   product: IconDeviceIpadHorizontal,
   productQuestion: IconDeviceIpadHorizontalQuestion,

   // currency
   try: IconCurrencyLira
};

export const tabler = {
   component: (props: IconProps) => {
      return h(props.tag, [h(tablerAliases[props.icon as keyof typeof tablerAliases])]);
   }
};
